# Judge Scores Display Implementation

## Overview
This document describes the implementation of judge scores display in the data grid, showing which groups move on or win based on their judgeScores data.

## Data Structure
Judge scores can be found in two locations:
1. **Direct on row**: `row.judgeScores` (array of score objects)
2. **Nested in classifications**: `row.classifications[].judgeScores` (array of score objects)

### Judge Score Object Structure
```javascript
{
  judgeId: "ObjectId",
  score: Number,
  movesOn: Boolean,
  isCommended: Boolean,
  isNational: Boolean,
  isCitation: Boolean,
  isState: Boolean,
  _id: "ObjectId"
}
```

## Implementation Details

### New Columns Added
1. **Judge Scores Column** (`judgeScores`)
   - **Display**: Shows all scores from both locations with visual indicators
   - **CSV Export**: Returns plain text format: "Score: 90 - Moves On, National"
   - Uses color coding: green background for "moves on", gray for others
   - Handles both nested and non-nested score structures

2. **Winner Status Column** (`winnerStatus`)
   - **Display**: Shows the highest priority status with colored badges
   - **CSV Export**: Returns plain text status: "NATIONAL", "MOVES ON", etc.
   - Priority order: National > Citation > State > Commended > Moves On > No Status
   - Uses colored badges with icons for visual clarity

### Visual Indicators
- **🏆 NATIONAL**: Red badge with trophy icon
- **🎖️ CITATION**: Orange badge with medal icon  
- **🏅 STATE**: Purple badge with medal icon
- **⭐ COMMENDED**: Blue badge with star icon
- **✓ MOVES ON**: Green badge with checkmark
- **No Status**: Gray badge

### Column Grouping
Both new columns are grouped under "Judging Results" in the data grid.

## Code Location
- **File**: `app/src/lib/tableHelper.mjs`
- **Lines**: 389-603 (Judge Scores and Winner Status columns)
- **Column Grouping**: Lines 887-900

## Testing
A test component has been created at `app/src/Components/JudgeScoresTest.jsx` to verify the display logic works correctly with sample data.

## Usage
The columns will automatically appear in the entries data grid and display:
- All judge scores for each group
- Clear visual indicators of which groups advance or win awards
- Proper handling of both nested and non-nested score structures

## CSV Export Format
- **Judge Scores**: "Score: 90 - Moves On, National" or "Score: 78 (A)" for nested scores
- **Winner Status**: "NATIONAL", "CITATION", "STATE", "COMMENDED", "MOVES ON", or "No Status"
- Multiple scores are separated by semicolons in the CSV export

## Benefits
1. **Clear Visual Feedback**: Users can quickly see which groups are advancing
2. **Comprehensive Display**: Shows all scores regardless of data structure
3. **Priority-Based Status**: Highest achievement is prominently displayed
4. **Responsive Design**: Compact display that works well in data grids
5. **CSV Export Ready**: Proper plain text values for spreadsheet compatibility
