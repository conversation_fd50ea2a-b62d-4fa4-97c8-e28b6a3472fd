import React from 'react';

// Test component to verify judge scores display logic
const JudgeScoresTest = () => {
  // Sample data based on the examples from judgeScores-examples.md
  const testData = [
    {
      _id: "test1",
      ensembleName: "Test Ensemble 1",
      judgeScores: [
        {
          judgeId: "68497c47cf19e414c27f634c",
          score: 90,
          movesOn: true,
          isCommended: false,
          isNational: false,
          isCitation: false,
          isState: false,
          _id: "688aa49086b8b47fee9308b5"
        }
      ],
      classifications: []
    },
    {
      _id: "test2", 
      ensembleName: "Test Ensemble 2",
      judgeScores: [],
      classifications: [
        {
          id: "643453f8dd98890aad8cef20",
          judgeScores: [
            {
              judgeId: "6845e91dcf19e414c27f5063",
              score: 78,
              movesOn: false,
              isCommended: false,
              isNational: false,
              isCitation: false,
              isState: false,
              _id: "687e51fe5498427b5887d431"
            }
          ],
          maskedName: "A"
        }
      ]
    },
    {
      _id: "test3",
      ensembleName: "Test Ensemble 3 - National Winner",
      judgeScores: [],
      classifications: [
        {
          id: "643453f8dd98890aad8cef22",
          judgeScores: [
            {
              judgeId: "6845e91dcf19e414c27f5063",
              score: 88,
              movesOn: true,
              isCommended: false,
              isNational: true,
              isCitation: false,
              isState: false,
              _id: "687e56465498427b5887dc51"
            }
          ],
          maskedName: "C"
        }
      ]
    }
  ];

  // Replicate the judge scores rendering logic from tableHelper.mjs
  const renderJudgeScores = (row) => {
    const allJudgeScores = [];
    
    // Collect judge scores from row.judgeScores (non-nested)
    if (row.judgeScores && row.judgeScores.length > 0) {
        allJudgeScores.push(...row.judgeScores);
    }
    
    // Collect judge scores from row.classifications[].judgeScores (nested)
    if (row.classifications && row.classifications.length > 0) {
        row.classifications.forEach(classification => {
            if (classification.judgeScores && classification.judgeScores.length > 0) {
                allJudgeScores.push(...classification.judgeScores.map(score => ({
                    ...score,
                    classificationName: classification.maskedName || 'Unknown'
                })));
            }
        });
    }
    
    if (allJudgeScores.length === 0) {
        return <span style={{ color: '#666', fontStyle: 'italic' }}>No scores</span>;
    }
    
    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2px', fontSize: '12px' }}>
            {allJudgeScores.map((score, index) => (
                <div key={index} style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '4px',
                    padding: '1px 4px',
                    borderRadius: '3px',
                    backgroundColor: score.movesOn ? '#e8f5e8' : '#f5f5f5'
                }}>
                    <span style={{ fontWeight: 'bold' }}>
                        {score.score || 0}
                    </span>
                    {score.classificationName && (
                        <span style={{ color: '#666', fontSize: '10px' }}>
                            ({score.classificationName})
                        </span>
                    )}
                    {score.movesOn && (
                        <span style={{ color: '#2e7d32', fontWeight: 'bold', fontSize: '10px' }}>
                            ✓ Moves On
                        </span>
                    )}
                    {score.isNational && (
                        <span style={{ color: '#d32f2f', fontWeight: 'bold', fontSize: '10px' }}>
                            🏆 National
                        </span>
                    )}
                    {score.isCitation && (
                        <span style={{ color: '#f57c00', fontWeight: 'bold', fontSize: '10px' }}>
                            🎖️ Citation
                        </span>
                    )}
                    {score.isCommended && (
                        <span style={{ color: '#1976d2', fontWeight: 'bold', fontSize: '10px' }}>
                            ⭐ Commended
                        </span>
                    )}
                    {score.isState && (
                        <span style={{ color: '#7b1fa2', fontWeight: 'bold', fontSize: '10px' }}>
                            🏅 State
                        </span>
                    )}
                </div>
            ))}
        </div>
    );
  };

  // Replicate the winner status rendering logic
  const renderWinnerStatus = (row) => {
    const allJudgeScores = [];
    
    // Collect all judge scores
    if (row.judgeScores && row.judgeScores.length > 0) {
        allJudgeScores.push(...row.judgeScores);
    }
    
    if (row.classifications && row.classifications.length > 0) {
        row.classifications.forEach(classification => {
            if (classification.judgeScores && classification.judgeScores.length > 0) {
                allJudgeScores.push(...classification.judgeScores);
            }
        });
    }
    
    // Check for highest priority status
    const hasNational = allJudgeScores.some(score => score.isNational);
    const hasCitation = allJudgeScores.some(score => score.isCitation);
    const hasCommended = allJudgeScores.some(score => score.isCommended);
    const hasState = allJudgeScores.some(score => score.isState);
    const movesOn = allJudgeScores.some(score => score.movesOn);
    
    if (hasNational) {
        return (
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '12px',
                backgroundColor: '#ffebee',
                border: '1px solid #d32f2f',
                color: '#d32f2f',
                fontWeight: 'bold',
                fontSize: '11px'
            }}>
                🏆 NATIONAL
            </div>
        );
    }
    
    if (hasCitation) {
        return (
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '12px',
                backgroundColor: '#fff3e0',
                border: '1px solid #f57c00',
                color: '#f57c00',
                fontWeight: 'bold',
                fontSize: '11px'
            }}>
                🎖️ CITATION
            </div>
        );
    }
    
    if (hasState) {
        return (
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '12px',
                backgroundColor: '#f3e5f5',
                border: '1px solid #7b1fa2',
                color: '#7b1fa2',
                fontWeight: 'bold',
                fontSize: '11px'
            }}>
                🏅 STATE
            </div>
        );
    }
    
    if (hasCommended) {
        return (
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '12px',
                backgroundColor: '#e3f2fd',
                border: '1px solid #1976d2',
                color: '#1976d2',
                fontWeight: 'bold',
                fontSize: '11px'
            }}>
                ⭐ COMMENDED
            </div>
        );
    }
    
    if (movesOn) {
        return (
            <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '12px',
                backgroundColor: '#e8f5e8',
                border: '1px solid #2e7d32',
                color: '#2e7d32',
                fontWeight: 'bold',
                fontSize: '11px'
            }}>
                ✓ MOVES ON
            </div>
        );
    }
    
    return (
        <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '4px',
            padding: '4px 8px',
            borderRadius: '12px',
            backgroundColor: '#f5f5f5',
            border: '1px solid #ccc',
            color: '#666',
            fontSize: '11px'
        }}>
            No Status
        </div>
    );
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Judge Scores Display Test</h2>
      <p>This component tests the judge scores display logic from tableHelper.mjs</p>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {testData.map((row) => (
          <div key={row._id} style={{ 
            border: '1px solid #ddd', 
            borderRadius: '8px', 
            padding: '16px',
            backgroundColor: '#fafafa'
          }}>
            <h3>{row.ensembleName}</h3>
            <div style={{ display: 'flex', gap: '20px', alignItems: 'flex-start' }}>
              <div>
                <h4>Judge Scores:</h4>
                {renderJudgeScores(row)}
              </div>
              <div>
                <h4>Winner Status:</h4>
                {renderWinnerStatus(row)}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default JudgeScoresTest;
