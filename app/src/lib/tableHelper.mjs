import React from "react"
import { GRID_STRING_COL_DEF, GridActionsCellItem, useGridApiContext } from "@mui/x-data-grid"
import AudioPlayer from "./AudioPlayer.mjs"
import { Edit, Archive, Restore } from "@mui/icons-material"
import { toJS } from "mobx"
import axios from "axios"

function setArrVal(arr, key, secondKey) {
    let returnVal = []
    arr.map(val => {
        if (!secondKey) {
            returnVal.push(val[key])
        } else {
            //console.log(arr)
            returnVal.push(val[key][secondKey])
        }
    })
    //console.log('setArrVal', key, secondKey, returnVal)
    return returnVal
}

const handleEditClick = (row) => {

}


function overrideValue(arr, index, key) {
    if(arr.length){
        return arr[index][key]
    }else{
        return false
    }
}

const audioPlayerType = {
    ...GRID_STRING_COL_DEF,
    type: 'custom',
    filterable: false,
    sortable: false,
    editable: false,
    display: 'flex',
    renderCell: (params) => <AudioPlayer {...params} />
}
const genDataGridProps = (type, data, selectOptions, archiveState, handleArchiveRestoreClick, contest) => {
    //console.log('contests', toJS(archiveState))
    //console.log('categories', toJS(selectOptions.categories))
    const currentYear = new Date().getFullYear()
    // console.log('genDataGridProps',data)
    let datagrid = {}
    if (type === 'entries') {
        const getOptions = (row, key, parentKey, parentColumn) => {
            let options = [{ value: 'error', label: `Error - ${key}` }]
            if (row === undefined) {
                for (const option of selectOptions[key]) {
                    options.push({ value: option._id, label: option.name })
                }
            } else {

                let validOptions = selectOptions[parentKey].find(obj => obj._id === row[parentColumn]._id)[key]
                for (const option of validOptions) {
                    options.push({ value: option._id, label: option.name })
                }
            }
            return options
        }    //console.log('doing this')
        const maxEntrySize = [];
        const trackSoloistColumns = {}
        let adminOverrides = []
        for (const entry of data) {
            entry.tracks.forEach((track, index) => {
                const maxSoloist = track.soloists ? track.soloists.length : 0
                maxEntrySize[index] = Math.max(maxEntrySize[index] || 0, maxSoloist);
            })

        }
        for (let index = 0; index < maxEntrySize.length; index++) {
            trackSoloistColumns[`track_${index}_soloist_columns`] = []
            for (let i = 0; i < maxEntrySize[index]; i++) {
                let toPush = [
                    {
                        field: `track_${index}_soloist_${i}_name`, headerName: 'Name', editable: true,
                        valueGetter: (value, row) => {
                            //console.log(toJS(row.tracks[index].soloists[i]))
                            if (row.tracks[index]?.soloists?.length && row.tracks[index].soloists[i]?.name) {
                                return row.tracks[index].soloists[i].name
                            } else {
                                return null
                            }
                        }
                    },
                    {
                        field: `track_${index}_soloist_${i}_instrument`, headerName: 'Instrument', editable: true,
                        valueGetter: (value, row) => {
                            if (row.tracks[index]?.soloists?.length && row.tracks[index].soloists[i]?.name) {
                                return row.tracks[index].soloists[i].instrument
                            } else {
                                return null
                            }
                        }
                    }
                ]
                trackSoloistColumns[`track_${index}_soloist_columns`].push(...toPush)

            }

            //console.log(toPush)
        }
        // let contestAward = "N/A"
        // let contestTitle = "N/A"
        // if (contest) {
        //     console.log('hae a contest')
        //     let contestAward = "isNational"
        //     let contestTitle = "National Winner"
        //     if (!contest.includes("Mark")) {
        //         contestAward = "isCitation"
        //         contestTitle = "Citation Winner"
        //     }
        // }

        //console.log('trackSoloists', trackSoloistColumns)
        let track_0_soloist_columns = trackSoloistColumns?.track_0_soloist_columns ? trackSoloistColumns.track_0_soloist_columns : []
        let track_1_soloist_columns = trackSoloistColumns?.track_1_soloist_columns ? trackSoloistColumns.track_1_soloist_columns : []
        let track_2_soloist_columns = trackSoloistColumns?.track_2_soloist_columns ? trackSoloistColumns.track_2_soloist_columns : []
        console.log('adminoverride', adminOverrides)
        datagrid.columns = [
            { field: '_id', headerName: 'Id' },
            {
                field: 'actions', type: "actions", headerName: 'Actions', cellClassName: 'actions',
                getActions: ({ row }) => {
                    return [
                        <GridActionsCellItem
                            icon={archiveState ? <Restore /> : <Archive />}
                            label={archiveState ? "Restore" : "Archive"}
                            onClick={() => handleArchiveRestoreClick(row, !archiveState)}
                        />
                    ];
                }
            },
            { field: 'invoicePaid', headerName: 'Invoice Paid', type: "boolean", editable: true },
            {
                field: 'checkNo',
                headerName: 'Check #',
                editable: true,
                renderCell: (params) => {
                    const isUrl = params.value && typeof params.value === 'string' &&
                        (params.value.startsWith('http://') || params.value.startsWith('https://'));

                    if (isUrl) {
                        return (
                            <a
                                href={params.value}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ textDecoration: 'underline', color: 'blue', cursor: 'pointer' }}
                                onClick={(e) => e.stopPropagation()}
                            >
                                {params.value}
                            </a>
                        );
                    } else {
                        return params.value;
                    }
                }
            },
            { field: 'scoresReceived', headerName: 'Received Scores', type: "boolean", editable: true },
            {
                field: 'classification', headerName: 'Classification', type: 'singleSelect', editable: false,
                valueGetter: (value, row) => {
                    let returnVal
                    if (row.classifications.length > 0) {
                        // console.log('huh', toJS(row))
                        returnVal = row.classifications[0].id._id
                    } else if (value) {
                        returnVal = value._id
                    } else {
                        returnVal = 'error'
                    }
                    return returnVal
                },
                valueOptions: ({ row }) => {
                    // console.log('valueOptions, classification')
                    return getOptions(row, 'classifications', 'categories', 'category')
                }
            },
            {
                field: 'maskedName', headerName: 'Masked Name', editable: true,
                valueGetter: (value, row) => {
                    // console.log('maskedName')
                    let returnVal
                    if (row.classifications.length) {
                        // console.log('array exists', toJS(row.classifications))
                        // console.log('maskedName', row.classification[0].maskedName)
                        returnVal = row.classifications[0].maskedName
                    } else {
                        returnVal = value
                    }
                    //console.log('maskedName', returnVal)
                    return returnVal
                },
            },
            {
                field: 'classifications[1].id._id', headerName: 'Additional Classification', type: 'singleSelect', editable: true,
                valueGetter: (value, row) => {
                    // console.log('additionalClassification')
                    if (row.classifications.length > 1) {
                        return row.classifications[1].id._id
                    } else {
                        return null
                    }
                },
                valueOptions: ({ row }) => {
                    return getOptions(row, 'classifications', 'categories', 'category')
                }
            },
            {
                field: 'classifications[1].maskedName', headerName: 'Masked Name', editable: true,
                valueGetter: (value, row) => {
                    //console.log('additionalMaskedName')
                    if (row.classifications.length > 1) {
                        return row.classifications[1].maskedName
                    } else {
                        return null
                    }
                },
            },
            {
                field: `adminOverrides.0.movesOn`, headerName: `Moves On`, type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 0, 'movesOn')
                }
            },
            {
                field: `adminOverrides.0.isCommended`, headerName: 'Commended', type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 0, 'isCommended')
                }
            },
            {
                field: `adminOverrides.0.isCitation`, headerName: "Citation", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 0, 'isCitation')
                }
            },
            {
                field: `adminOverrides.0.isNational`, headerName: "National", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 0, 'isNational')
                }
            },
            {
                field: `adminOverrides.0.isState`, headerName: "State", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 0, 'isState')
                }
            },
            {
                field: `adminOverrides.1.movesOn`, headerName: `Moves On`, type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 1, 'movesOn')
                }
            },
            {
                field: `adminOverrides.1.isCommended`, headerName: 'Commended', type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 1, 'isCommended')
                }
            },
            {
                field: `adminOverrides.1.isCitation`, headerName: "Citation", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 1, 'isCitation')
                }
            },
            {
                field: `adminOverrides.1.isNational`, headerName: "National", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 1, 'isNational')
                }
            },
            {
                field: `adminOverrides.1.isState`, headerName: "State", type: "boolean", editable: true,
                valueGetter: (value, row) => {
                    return overrideValue(row.adminOverrides, 1, 'isState')
                }
            },
            { field: 'email', headerName: 'Email' },
            { field: 'cellphone', headerName: 'Phone', editable: true },
            {
                field: 'mailingAddress.street',
                headerName: 'Street',
                editable: true,
                valueGetter: (value,row) => row?.mailingAddress?.street || ''
            },
            {
                field: 'mailingAddress.city',
                headerName: 'City',
                editable: true,
                valueGetter: (value,row) => row?.mailingAddress?.city || ''
            },
            {
                field: 'mailingAddress.state',
                headerName: 'State',
                editable: true,
                valueGetter: (value,row) => row?.mailingAddress?.state || ''
            },
            {
                field: 'mailingAddress.zip',
                headerName: 'Zip',
                editable: true,
                valueGetter: (value,row) => row?.mailingAddress?.zip || ''
            },
            {
                field: 'shippingAddress.street',
                headerName: 'Street',
                editable: true,
                valueGetter: (value,row) => row?.shippingAddress?.street || ''
            },
            {
                field: 'shippingAddress.city',
                headerName: 'City',
                editable: true,
                valueGetter: (value,row) => row?.shippingAddress?.city || ''
            },
            {
                field: 'shippingAddress.state',
                headerName: 'State',
                editable: true,
                valueGetter: (value,row) => row?.shippingAddress?.state || ''
            },
            {
                field: 'shippingAddress.zip',
                headerName: 'Zip',
                editable: true,
                valueGetter: (value,row) => row?.shippingAddress?.zip || ''
            },
            { field: 'director', headerName: "Director(s)", editable: true },
            { field: 'schoolName', headerName: "School", editable: true },
            { field: 'ensembleName', headerName: 'Ensemble Name', editable: true },
            {
                field: 'contest', headerName: 'Contest', type: 'singleSelect', editable: false,
                valueGetter: (value, row) => {
                    return value._id
                },
                valueOptions: ({ row }) => {
                    const options = []
                    for (const contest of selectOptions.contests) {
                        options.push({ value: contest._id, label: contest.name })
                    }
                    return options
                }
            },
            {
                field: 'category', headerName: 'Category', type: 'singleSelect', editable: false,
                valueGetter: (value, row) => {
                    return value._id
                },
                valueOptions: ({ row }) => {
                    return getOptions(row, 'categories', 'contests', 'contest')
                }
            },

            // {
            //     field: 'maskedName', headerName: 'Masked Name', editable: true,
            //     valueGetter: (value, row) => {
            //         // console.log('maskedName')
            //         let returnVal
            //         if (row.classifications.length) {
            //             // console.log('array exists', toJS(row.classifications))
            //             // console.log('maskedName', row.classification[0].maskedName)
            //             returnVal = row.classifications[0].maskedName
            //         } else {
            //             returnVal = value
            //         }
            //         //console.log('maskedName', returnVal)
            //         return returnVal
            //     },
            // },

            // {
            //     field: 'classifications[1].maskedName', headerName: 'Masked Name', editable: true,
            //     valueGetter: (value, row) => {
            //         //console.log('additionalMaskedName')
            //         if (row.classifications.length > 1) {
            //             return row.classifications[1].maskedName
            //         } else {
            //             return null
            //         }
            //     },
            // },
            {
                field: 'judgeScores', headerName: 'Judge Scores', editable: false, width: 200,
                renderCell: (params) => {
                    const { row } = params;
                    const allJudgeScores = [];

                    // Collect judge scores from row.judgeScores (non-nested)
                    if (row.judgeScores && row.judgeScores.length > 0) {
                        allJudgeScores.push(...row.judgeScores);
                    }

                    // Collect judge scores from row.classifications[].judgeScores (nested)
                    if (row.classifications && row.classifications.length > 0) {
                        row.classifications.forEach(classification => {
                            if (classification.judgeScores && classification.judgeScores.length > 0) {
                                allJudgeScores.push(...classification.judgeScores.map(score => ({
                                    ...score,
                                    classificationName: classification.maskedName || 'Unknown'
                                })));
                            }
                        });
                    }

                    if (allJudgeScores.length === 0) {
                        return <span style={{ color: '#666', fontStyle: 'italic' }}>No scores</span>;
                    }

                    return (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '2px', fontSize: '12px' }}>
                            {allJudgeScores.map((score, index) => (
                                <div key={index} style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px',
                                    padding: '1px 4px',
                                    borderRadius: '3px',
                                    backgroundColor: score.movesOn ? '#e8f5e8' : '#f5f5f5'
                                }}>
                                    <span style={{ fontWeight: 'bold' }}>
                                        {score.score || 0}
                                    </span>
                                    {score.classificationName && (
                                        <span style={{ color: '#666', fontSize: '10px' }}>
                                            ({score.classificationName})
                                        </span>
                                    )}
                                    {score.movesOn && (
                                        <span style={{ color: '#2e7d32', fontWeight: 'bold', fontSize: '10px' }}>
                                            ✓ Moves On
                                        </span>
                                    )}
                                    {score.isNational && (
                                        <span style={{ color: '#d32f2f', fontWeight: 'bold', fontSize: '10px' }}>
                                            🏆 National
                                        </span>
                                    )}
                                    {score.isCitation && (
                                        <span style={{ color: '#f57c00', fontWeight: 'bold', fontSize: '10px' }}>
                                            🎖️ Citation
                                        </span>
                                    )}
                                    {score.isCommended && (
                                        <span style={{ color: '#1976d2', fontWeight: 'bold', fontSize: '10px' }}>
                                            ⭐ Commended
                                        </span>
                                    )}
                                    {score.isState && (
                                        <span style={{ color: '#7b1fa2', fontWeight: 'bold', fontSize: '10px' }}>
                                            🏅 State
                                        </span>
                                    )}
                                </div>
                            ))}
                        </div>
                    );
                }
            },
            {
                field: 'winnerStatus', headerName: 'Status', editable: false, width: 120,
                renderCell: (params) => {
                    const { row } = params;
                    const allJudgeScores = [];

                    // Collect all judge scores
                    if (row.judgeScores && row.judgeScores.length > 0) {
                        allJudgeScores.push(...row.judgeScores);
                    }

                    if (row.classifications && row.classifications.length > 0) {
                        row.classifications.forEach(classification => {
                            if (classification.judgeScores && classification.judgeScores.length > 0) {
                                allJudgeScores.push(...classification.judgeScores);
                            }
                        });
                    }

                    // Check for highest priority status
                    const hasNational = allJudgeScores.some(score => score.isNational);
                    const hasCitation = allJudgeScores.some(score => score.isCitation);
                    const hasCommended = allJudgeScores.some(score => score.isCommended);
                    const hasState = allJudgeScores.some(score => score.isState);
                    const movesOn = allJudgeScores.some(score => score.movesOn);

                    if (hasNational) {
                        return (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#ffebee',
                                border: '1px solid #d32f2f',
                                color: '#d32f2f',
                                fontWeight: 'bold',
                                fontSize: '11px'
                            }}>
                                🏆 NATIONAL
                            </div>
                        );
                    }

                    if (hasCitation) {
                        return (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#fff3e0',
                                border: '1px solid #f57c00',
                                color: '#f57c00',
                                fontWeight: 'bold',
                                fontSize: '11px'
                            }}>
                                🎖️ CITATION
                            </div>
                        );
                    }

                    if (hasState) {
                        return (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#f3e5f5',
                                border: '1px solid #7b1fa2',
                                color: '#7b1fa2',
                                fontWeight: 'bold',
                                fontSize: '11px'
                            }}>
                                🏅 STATE
                            </div>
                        );
                    }

                    if (hasCommended) {
                        return (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#e3f2fd',
                                border: '1px solid #1976d2',
                                color: '#1976d2',
                                fontWeight: 'bold',
                                fontSize: '11px'
                            }}>
                                ⭐ COMMENDED
                            </div>
                        );
                    }

                    if (movesOn) {
                        return (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                backgroundColor: '#e8f5e8',
                                border: '1px solid #2e7d32',
                                color: '#2e7d32',
                                fontWeight: 'bold',
                                fontSize: '11px'
                            }}>
                                ✓ MOVES ON
                            </div>
                        );
                    }

                    return (
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            backgroundColor: '#f5f5f5',
                            border: '1px solid #ccc',
                            color: '#666',
                            fontSize: '11px'
                        }}>
                            No Status
                        </div>
                    );
                }
            },
            // Track 0
            {
                field: 'track_0_id',
                headerName: 'Id',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0]._id;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_0_title',
                headerName: 'Title',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0].title;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_0_composer',
                headerName: 'Composer',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0].composer;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_0_duration',
                headerName: 'Duration',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0].duration;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_0_venue',
                headerName: 'Venue',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0].venue;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_0_minioSrc',
                ...audioPlayerType,
                headerName: 'Recording',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 0) {
                        return row.tracks[0].minioSrc;
                    } else {
                        return null;
                    }
                }
            },
            ...track_0_soloist_columns,
            // {
            //     field: 'track_0_soloists',
            //     headerName: 'Soloists',
            //     valueGetter: (value, row) => {
            //         if (row.tracks && row.tracks.length > 0 && row.tracks[0].soloists && row.tracks[0].soloists.length > 0) {
            //             return row.tracks[0].soloists.map(soloist => `${soloist.name} - ${soloist.instrument}`);
            //         } else {
            //             return null;
            //         }
            //     }
            // },
            // Track 1
            {
                field: 'track_1_id',
                headerName: 'Id',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1]._id;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_1_title',
                headerName: 'Title',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1].title;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_1_composer',
                headerName: 'Composer',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1].composer;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_1_duration',
                headerName: 'Duration',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1].duration;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_1_venue',
                headerName: 'Venue',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1].venue;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_1_minioSrc',
                ...audioPlayerType,
                headerName: 'Recording',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 1) {
                        return row.tracks[1].minioSrc;
                    } else {
                        return null;
                    }
                }
            },
            ...track_1_soloist_columns,
            {
                field: 'track_2_id',
                headerName: 'Id',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2]._id;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_2_title',
                headerName: 'Title',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2].title;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_2_composer',
                headerName: 'Composer',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2].composer;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_2_duration',
                headerName: 'Duration',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2].duration;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_2_venue',
                headerName: 'Venue',
                editable: true,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2].venue;
                    } else {
                        return null;
                    }
                }
            },
            {
                field: 'track_2_minioSrc',
                ...audioPlayerType,
                headerName: 'Recording',
                editable: false,
                valueGetter: (value, row) => {
                    if (row.tracks && row.tracks.length > 2) {
                        return row.tracks[2].minioSrc;
                    } else {
                        return null;
                    }
                }
            },
            ...track_2_soloist_columns
        ]
        datagrid.columnGroupingModel = [
            {
                groupId: "Admin Section",
                children: [
                    { field: 'actions' },
                    {
                        groupId: 'Accounting',
                        children: [
                            { field: 'invoicePaid' },
                            { field: 'checkNo' },
                        ]
                    },
                    { field: 'scoresReceived' },
                ]
            },
            {
                groupId: 'Contact Info',
                children: [
                    { field: 'email' },
                    { field: 'cellphone' },
                    {
                        groupId: 'Mailing Address',
                        children: [
                            { field: 'mailingAddress.street' },
                            { field: 'mailingAddress.city' },
                            { field: 'mailingAddress.state' },
                            { field: 'mailingAddress.zip' }
                        ]
                    },
                    {
                        groupId: 'Shipping Address',
                        children: [
                            { field: 'shippingAddress.street' },
                            { field: 'shippingAddress.city' },
                            { field: 'shippingAddress.state' },
                            { field: 'shippingAddress.zip' }
                        ]
                    }
                ]
            },
            {
                groupId: 'Ensemble Info',
                children: [
                    { field: 'director' },
                    { field: 'schoolName' },
                    { field: 'ensembleName' },
                    { field: 'contest' },
                    { field: 'category' },
                    { field: 'classification' },
                    { field: 'maskedName' },
                    { field: 'classifications[1].id._id' },
                    { field: 'classifications[1].maskedName' }
                ]
            },
            {
                groupId: 'Judging Results',
                children: [
                    { field: 'judgeScores' },
                    { field: 'winnerStatus' }
                ]
            },
            {
                groupId: 'Selection 1',
                yoboid: 0,
                children: [{
                    groupId: 'selection_1',
                    headerName: `Selection Info`,
                    children: [{ field: 'track_0_id' },
                    { field: 'track_0_title' },
                    { field: 'track_0_composer' },
                    { field: 'track_0_duration' },
                    { field: 'track_0_venue' },
                    { field: 'track_0_minioSrc' }],
                }]
            },
            {
                groupId: 'Selection 2',
                yoboid: 1,
                children: [{
                    groupId: 'selection_2',
                    headerName: `Selection Info`,
                    children: [{ field: 'track_1_id' },
                    { field: 'track_1_title' },
                    { field: 'track_1_composer' },
                    { field: 'track_1_duration' },
                    { field: 'track_1_venue' },
                    { field: 'track_1_minioSrc' }],
                }]
            }
        ]
        let columnVisibilityModel = {
            _id: false,
            track_0_id: false,
            track_1_id: false,
            track_2_id: false,
        }
        const hasMaxTracks = data.some(entry => entry.tracks.length === 3)
        if (hasMaxTracks) {
            //console.log('has 3 tracks')
            //console.log(datagrid.columns)
            datagrid.columns.push(...track_2_soloist_columns)
            datagrid.columnGroupingModel.push({
                groupId: 'Selection 3',
                yoboid: 2,
                children: [{
                    groupId: 'selection_3',
                    headerName: `Selection Info`,
                    children: [{ field: 'track_2_id' },
                    { field: 'track_2_title' },
                    { field: 'track_2_composer' },
                    { field: 'track_2_duration' },
                    { field: 'track_2_venue' },
                    { field: 'track_2_minioSrc' }],
                }]
            })
        } else {
            //console.log('doesnt have')
            columnVisibilityModel.track_2_id = false
            columnVisibilityModel.track_2_title = false
            columnVisibilityModel.track_2_composer = false
            columnVisibilityModel.track_2_venue = false
            columnVisibilityModel.track_2_duration = false
            columnVisibilityModel.track_2_soloists = false
            columnVisibilityModel.track_2_minioSrc = false
        }

        const hasArrayofClassifications = data.some(entry => entry.classifications.length)

        if (!hasArrayofClassifications) {
            console.log('should hide')
            columnVisibilityModel[`classifications[1].id._id`] = false
            columnVisibilityModel[`classifications[1].maskedName`] = false
            columnVisibilityModel['adminOverrides.1.movesOn'] = false
            columnVisibilityModel['adminOverrides.1.isCommended'] = false
            columnVisibilityModel[`adminOverrides.1.isCitation}`] = false
            columnVisibilityModel[`adminOverrides.1.isNational}`] = false
            columnVisibilityModel['adminOverrides.1.isState'] = false

        }


        datagrid.columnGroupingModel.forEach(group => {
            //console.log(group.groupId, group.yoboid, !group.yoboid === undefined, !group.yoboid === null)
            if (group.yoboid !== undefined || group.yoboid !== null) {
                //console.log('trying to add soloist group header')
                const children = trackSoloistColumns[`track_${group.yoboid}_soloist_columns`]?.map(column => ({ field: column.field })) || [];
                if (children.length) {
                    //console.log('should be adding more groups')
                    const soloistGroup = {
                        groupId: `soloists_${group.yoboid}`,
                        headerName: 'Soloists',
                        children: children
                    };
                    group.children.push(soloistGroup);
                }
            }
        });


        //console.log(datagrid.columnGroupingModel)
        datagrid.rows = [...data]
        // datagrid.initialState={
        //     sorting: {
        //         sortModel: [{ field: 'classification', sort: 'desc' }],
        //       }
        // }
        datagrid.columnVisibilityModel = { ...columnVisibilityModel }

    } else if (type === 'users') {
        // console.log('genDataGridProps')
        // console.log(toJS(data))
        datagrid.columns = [
            { field: "_id", headerName: "Id" },
            { field: "email", headerName: "Email" },
            {
                field: "currGroups", headerName: `${currentYear} Entries`,
                valueGetter: (value) => {
                    return value.length
                }
            },
            {
                field: "previousGroups", headerName: 'Past Entries',
                valueGetter: (value) => {
                    return value.length
                }
            },
            {
                field: "lastLoggedIn", headerName: 'Last Logged In', type: "date",
                valueGetter: (value) => value && new Date(value)
            },
            { field: "isAdmin", headerName: 'Admin User', type: "boolean" }
        ]
        datagrid.rows = [...data]
        datagrid.columnVisibilityModel = {
            _id: false
        }
    }
    // console.log('tableHelper', datagrid)
    return datagrid
}
export { genDataGridProps }