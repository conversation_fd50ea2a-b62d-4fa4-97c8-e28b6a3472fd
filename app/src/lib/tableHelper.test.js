import { render, screen } from '@testing-library/react';
import { genDataGridProps } from './tableHelper.mjs';

// Mock data based on judgeScores-examples.md
const mockSelectOptions = {
  contests: [{ _id: 'contest1', name: 'Test Contest' }],
  categories: [{ _id: 'cat1', name: 'Test Category' }]
};

const mockData = [
  {
    _id: 'test1',
    ensembleName: 'Test Ensemble 1',
    email: '<EMAIL>',
    tracks: [],
    classifications: [],
    adminOverrides: [],
    judgeScores: [
      {
        judgeId: '68497c47cf19e414c27f634c',
        score: 90,
        movesOn: true,
        isCommended: false,
        isNational: false,
        isCitation: false,
        isState: false,
        _id: '688aa49086b8b47fee9308b5'
      }
    ],
    contest: { _id: 'contest1' },
    category: { _id: 'cat1' }
  },
  {
    _id: 'test2',
    ensembleName: 'Test Ensemble 2',
    email: '<EMAIL>',
    tracks: [],
    adminOverrides: [],
    judgeScores: [],
    classifications: [
      {
        id: { _id: '643453f8dd98890aad8cef20' },
        judgeScores: [
          {
            judgeId: '6845e91dcf19e414c27f5063',
            score: 78,
            movesOn: false,
            isCommended: false,
            isNational: false,
            isCitation: false,
            isState: false,
            _id: '687e51fe5498427b5887d431'
          }
        ],
        maskedName: 'A'
      }
    ],
    contest: { _id: 'contest1' },
    category: { _id: 'cat1' }
  },
  {
    _id: 'test3',
    ensembleName: 'Test Ensemble 3 - National Winner',
    email: '<EMAIL>',
    tracks: [],
    adminOverrides: [],
    judgeScores: [],
    classifications: [
      {
        id: { _id: '643453f8dd98890aad8cef22' },
        judgeScores: [
          {
            judgeId: '6845e91dcf19e414c27f5063',
            score: 88,
            movesOn: true,
            isCommended: false,
            isNational: true,
            isCitation: false,
            isState: false,
            _id: '687e56465498427b5887dc51'
          }
        ],
        maskedName: 'C'
      }
    ],
    contest: { _id: 'contest1' },
    category: { _id: 'cat1' }
  }
];

describe('tableHelper - Judge Scores', () => {
  test('genDataGridProps generates correct columns for entries', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    
    // Check that judge scores columns exist
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');
    
    expect(judgeScoresColumn).toBeDefined();
    expect(judgeScoresColumn.headerName).toBe('Judge Scores');
    expect(judgeScoresColumn.editable).toBe(false);
    
    expect(winnerStatusColumn).toBeDefined();
    expect(winnerStatusColumn.headerName).toBe('Status');
    expect(winnerStatusColumn.editable).toBe(false);
  });

  test('judge scores column renders correctly for non-nested scores', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');
    
    // Test with first mock data item (non-nested scores)
    const params = { row: mockData[0] };
    const rendered = judgeScoresColumn.renderCell(params);
    
    expect(rendered).toBeDefined();
    expect(rendered.props.children.props.children).toHaveLength(1); // One score
  });

  test('judge scores column renders correctly for nested scores', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');
    
    // Test with second mock data item (nested scores)
    const params = { row: mockData[1] };
    const rendered = judgeScoresColumn.renderCell(params);
    
    expect(rendered).toBeDefined();
    expect(rendered.props.children.props.children).toHaveLength(1); // One score from classification
  });

  test('winner status column shows correct status for national winner', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');
    
    // Test with third mock data item (national winner)
    const params = { row: mockData[2] };
    const rendered = winnerStatusColumn.renderCell(params);
    
    expect(rendered).toBeDefined();
    expect(rendered.props.children).toContain('🏆 NATIONAL');
    expect(rendered.props.style.backgroundColor).toBe('#ffebee');
    expect(rendered.props.style.color).toBe('#d32f2f');
  });

  test('winner status column shows moves on status', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');
    
    // Test with first mock data item (moves on)
    const params = { row: mockData[0] };
    const rendered = winnerStatusColumn.renderCell(params);
    
    expect(rendered).toBeDefined();
    expect(rendered.props.children).toContain('✓ MOVES ON');
    expect(rendered.props.style.backgroundColor).toBe('#e8f5e8');
    expect(rendered.props.style.color).toBe('#2e7d32');
  });

  test('winner status column shows no status for non-advancing groups', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');
    
    // Test with second mock data item (no advancement)
    const params = { row: mockData[1] };
    const rendered = winnerStatusColumn.renderCell(params);
    
    expect(rendered).toBeDefined();
    expect(rendered.props.children).toContain('No Status');
    expect(rendered.props.style.backgroundColor).toBe('#f5f5f5');
    expect(rendered.props.style.color).toBe('#666');
  });

  test('judging results column group is included', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    
    const judgingResultsGroup = result.columnGroupingModel.find(
      group => group.groupId === 'Judging Results'
    );
    
    expect(judgingResultsGroup).toBeDefined();
    expect(judgingResultsGroup.children).toEqual([
      { field: 'judgeScores' },
      { field: 'winnerStatus' }
    ]);
  });

  test('handles empty judge scores gracefully', () => {
    const emptyScoreData = [{
      _id: 'empty',
      ensembleName: 'Empty Ensemble',
      email: '<EMAIL>',
      tracks: [],
      classifications: [],
      adminOverrides: [],
      judgeScores: [],
      contest: { _id: 'contest1' },
      category: { _id: 'cat1' }
    }];

    const result = genDataGridProps('entries', emptyScoreData, mockSelectOptions, false, jest.fn());
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');

    const params = { row: emptyScoreData[0] };
    const rendered = judgeScoresColumn.renderCell(params);

    expect(rendered).toBeDefined();
    expect(rendered.props.children).toBe('No scores');
    expect(rendered.props.style.color).toBe('#666');
    expect(rendered.props.style.fontStyle).toBe('italic');
  });

  test('valueGetter returns correct text for CSV export - judge scores', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');

    // Test with first mock data item (non-nested scores, moves on)
    const csvValue1 = judgeScoresColumn.valueGetter(null, mockData[0]);
    expect(csvValue1).toBe('Score: 90 - Moves On');

    // Test with second mock data item (nested scores, no advancement)
    const csvValue2 = judgeScoresColumn.valueGetter(null, mockData[1]);
    expect(csvValue2).toBe('Score: 78 (A)');

    // Test with third mock data item (nested scores, national winner)
    const csvValue3 = judgeScoresColumn.valueGetter(null, mockData[2]);
    expect(csvValue3).toBe('Score: 88 (C) - Moves On, National');
  });

  test('valueGetter returns correct text for CSV export - winner status', () => {
    const result = genDataGridProps('entries', mockData, mockSelectOptions, false, jest.fn());
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');

    // Test with first mock data item (moves on)
    const csvValue1 = winnerStatusColumn.valueGetter(null, mockData[0]);
    expect(csvValue1).toBe('MOVES ON');

    // Test with second mock data item (no advancement)
    const csvValue2 = winnerStatusColumn.valueGetter(null, mockData[1]);
    expect(csvValue2).toBe('No Status');

    // Test with third mock data item (national winner)
    const csvValue3 = winnerStatusColumn.valueGetter(null, mockData[2]);
    expect(csvValue3).toBe('NATIONAL');
  });

  test('valueGetter handles empty scores for CSV export', () => {
    const emptyScoreData = [{
      _id: 'empty',
      ensembleName: 'Empty Ensemble',
      email: '<EMAIL>',
      tracks: [],
      classifications: [],
      adminOverrides: [],
      judgeScores: [],
      contest: { _id: 'contest1' },
      category: { _id: 'cat1' }
    }];

    const result = genDataGridProps('entries', emptyScoreData, mockSelectOptions, false, jest.fn());
    const judgeScoresColumn = result.columns.find(col => col.field === 'judgeScores');
    const winnerStatusColumn = result.columns.find(col => col.field === 'winnerStatus');

    const judgeScoresCsvValue = judgeScoresColumn.valueGetter(null, emptyScoreData[0]);
    const winnerStatusCsvValue = winnerStatusColumn.valueGetter(null, emptyScoreData[0]);

    expect(judgeScoresCsvValue).toBe('No scores');
    expect(winnerStatusCsvValue).toBe('No Status');
  });
});
